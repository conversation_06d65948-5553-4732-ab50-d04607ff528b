#!/usr/bin/env python3
"""
ImageNet100评估脚本
使用方法:
python eval_imagenet100.py --data-path /path/to/your/imagenet100 --resume /path/to/checkpoint.pth
"""

import os
import subprocess
import argparse

def main():
    parser = argparse.ArgumentParser(description='Evaluate vHeat on ImageNet100')
    parser.add_argument('--data-path', type=str, required=True, 
                        help='Path to ImageNet100 dataset')
    parser.add_argument('--resume', type=str, required=True,
                        help='Path to checkpoint file')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='Batch size per GPU (default: 64)')
    parser.add_argument('--gpus', type=int, default=1,
                        help='Number of GPUs to use (default: 1)')
    parser.add_argument('--model-size', type=str, default='tiny', 
                        choices=['tiny', 'small', 'base'],
                        help='Model size (default: tiny)')
    parser.add_argument('--output', type=str, default='./eval_output',
                        help='Output directory (default: ./eval_output)')
    parser.add_argument('--single-gpu', action='store_true',
                        help='Use single GPU evaluation (no distributed)')

    args = parser.parse_args()
    
    # 检查数据集路径
    if not os.path.exists(args.data_path):
        raise ValueError(f"Dataset path does not exist: {args.data_path}")
    
    # 检查checkpoint文件
    if not os.path.exists(args.resume):
        raise ValueError(f"Checkpoint file does not exist: {args.resume}")
    
    val_path = os.path.join(args.data_path, 'val')
    if not os.path.exists(val_path):
        raise ValueError(f"Validation directory does not exist: {val_path}")
    
    # 检查类别数
    val_classes = len([d for d in os.listdir(val_path) 
                      if os.path.isdir(os.path.join(val_path, d))])
    
    print(f"Found {val_classes} classes in validation set")
    
    # 选择配置文件
    if args.model_size == 'tiny':
        config_file = 'classification/configs/vHeat/vHeat_tiny_imagenet100.yaml'
    elif args.model_size == 'small':
        config_file = 'classification/configs/vHeat/vHeat_small_imagenet100.yaml'
    else:  # base
        config_file = 'classification/configs/vHeat/vHeat_base_imagenet100.yaml'
    
    # 构建评估命令
    if args.single_gpu or args.gpus == 1:
        # 单GPU评估，不使用分布式
        cmd = [
            'python', 'classification/main.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output,
            '--resume', args.resume,
            '--eval',
            '--model_ema', 'False',
            '--local_rank', '0'
        ]
    else:
        # 多GPU分布式评估，使用torchrun
        cmd = [
            'torchrun',
            '--nnodes=1',
            '--nproc_per_node=' + str(args.gpus),
            '--master_addr=127.0.0.1',
            '--master_port=29502',
            'classification/main.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output,
            '--resume', args.resume,
            '--eval',
            '--model_ema', 'False'
        ]
    
    # 添加自定义配置选项
    cmd.extend([
        '--opts',
        'MODEL.NUM_CLASSES', str(val_classes)
    ])
    
    print("Running evaluation command:")
    print(' '.join(cmd))
    print()
    
    # 执行评估
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Evaluation failed with error code {e.returncode}")
        return e.returncode
    
    print("Evaluation completed successfully!")
    return 0

if __name__ == '__main__':
    exit(main())
