# 继续训练与早停策略

本文档介绍如何在已训练好的模型基础上继续训练，并添加早停策略来防止过拟合。

## 功能特性

### 1. 继续训练
- 从已有的checkpoint继续训练
- 自动检测最新的checkpoint文件
- 支持增加训练轮数
- 保持原有的训练配置

### 2. 早停策略
- **监控阶段**: 每个epoch后检查验证准确率
- **改善判断**: 如果准确率提升超过设定阈值，重置等待计数器
- **早停触发**: 当连续多个epoch没有足够改善时，自动停止训练
- **最佳模型保存**: 自动保存最佳模型权重

## 早停策略详细说明

### 工作原理
1. **监控阶段**: 每个epoch训练完成后，在验证集上评估模型准确率
2. **改善检查**: 
   - 如果当前准确率比历史最佳准确率提升超过 `min_improvement`（默认0.01%），则：
     - 更新最佳准确率
     - 重置等待计数器为0
     - 保存当前模型为最佳模型
   - 如果改善不足，则等待计数器+1
3. **早停判断**: 当等待计数器达到 `patience`（默认15个epoch）时，触发早停

### 参数说明
- `patience`: 早停耐心值，连续多少个epoch没有改善就停止训练（默认：15）
- `min_improvement`: 最小改善阈值，单位为百分比（默认：0.01%）

## 使用方法

### 方法1: 使用便捷脚本（推荐）

```bash
# 运行示例脚本
python example_continue_training.py
```

该脚本会：
1. 自动检查checkpoint目录
2. 显示训练配置
3. 询问用户确认后开始训练

### 方法2: 直接使用继续训练脚本

```bash
python continue_train_with_early_stopping.py \
    --checkpoint-dir ./output1 \
    --data-path /path/to/your/imagenet100 \
    --additional-epochs 50 \
    --patience 15 \
    --min-improvement 0.01 \
    --batch-size 256 \
    --output ./output_continued \
    --single-gpu
```

### 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--checkpoint-dir` | 包含已训练模型checkpoint的目录 | 必需 |
| `--data-path` | ImageNet100数据集路径 | 必需 |
| `--additional-epochs` | 额外训练的轮数 | 50 |
| `--patience` | 早停耐心值（epoch数） | 15 |
| `--min-improvement` | 最小改善阈值（%） | 0.01 |
| `--batch-size` | 批次大小 | 256 |
| `--output` | 输出目录 | ./output_continued |
| `--single-gpu` | 使用单GPU训练 | False |
| `--disable-ema` | 禁用EMA | False |

## 训练日志示例

```
[2025-08-22 10:30:15] Early stopping enabled: patience=15, min_improvement=0.01%
[2025-08-22 10:35:20] Accuracy of the network on the 5000 test images: 85.3%
[2025-08-22 10:35:20] Early stopping: Accuracy improved by 0.02% to 85.30%, resetting patience counter
[2025-08-22 10:35:20] Max accuracy: 85.30%

[2025-08-22 10:40:25] Accuracy of the network on the 5000 test images: 85.2%
[2025-08-22 10:40:25] Early stopping: No sufficient improvement (current: 85.20%, best: 85.30%), patience: 1/15
[2025-08-22 10:40:25] Max accuracy: 85.30%

...

[2025-08-22 12:15:30] Accuracy of the network on the 5000 test images: 85.1%
[2025-08-22 12:15:30] Early stopping: No sufficient improvement (current: 85.10%, best: 85.30%), patience: 15/15
[2025-08-22 12:15:30] Early stopping triggered! Best accuracy: 85.30% at epoch 98
[2025-08-22 12:15:30] Early stopping triggered, stopping training...
```

## 输出文件

训练完成后，在输出目录中会包含：
- `ckpt_epoch_*.pth`: 定期保存的checkpoint
- `ckpt_best.pth`: 最佳模型checkpoint
- `config.json`: 训练配置文件
- `log_rank0.txt`: 详细训练日志

## 注意事项

1. **数据集路径**: 请确保数据集路径正确，包含 `train` 和 `val` 子目录
2. **GPU内存**: 根据您的GPU内存调整batch_size
3. **checkpoint兼容性**: 确保使用的checkpoint与当前代码版本兼容
4. **早停参数调整**: 
   - 对于小数据集，可以减小patience值
   - 对于大数据集，可以增加patience值
   - min_improvement可以根据模型性能调整

## 故障排除

### 常见问题

1. **找不到checkpoint文件**
   ```
   错误: No checkpoint files found in ./output1
   ```
   解决: 检查checkpoint目录路径是否正确

2. **数据集路径错误**
   ```
   错误: Dataset path does not exist: /path/to/dataset
   ```
   解决: 确认数据集路径正确，包含train和val子目录

3. **GPU内存不足**
   ```
   错误: CUDA out of memory
   ```
   解决: 减小batch_size参数

4. **配置文件不兼容**
   ```
   错误: Config file not found: config.json
   ```
   解决: 确保checkpoint目录包含完整的训练输出文件
