#!/usr/bin/env python3
"""
继续训练脚本，带早停策略
基于已训练的模型继续训练，增加训练轮数，并添加早停策略
"""

import os
import subprocess
import argparse
import json
import yaml
import shutil
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description='Continue training vHeat with early stopping')
    parser.add_argument('--checkpoint-dir', type=str, required=True,
                        help='Directory containing the trained model checkpoint')
    parser.add_argument('--data-path', type=str, required=True, 
                        help='Path to ImageNet100 dataset')
    parser.add_argument('--additional-epochs', type=int, default=50,
                        help='Additional epochs to train (default: 50)')
    parser.add_argument('--patience', type=int, default=15,
                        help='Early stopping patience (default: 15)')
    parser.add_argument('--min-improvement', type=float, default=0.01,
                        help='Minimum improvement threshold in % (default: 0.01)')
    parser.add_argument('--batch-size', type=int, default=256,
                        help='Batch size per GPU (default: 256)')
    parser.add_argument('--output', type=str, default='./output_continued',
                        help='Output directory (default: ./output_continued)')
    parser.add_argument('--single-gpu', action='store_true',
                        help='Use single GPU training (no distributed)')
    parser.add_argument('--disable-ema', action='store_true',
                        help='Disable EMA')

    args = parser.parse_args()
    
    # 检查checkpoint目录
    if not os.path.exists(args.checkpoint_dir):
        raise ValueError(f"Checkpoint directory does not exist: {args.checkpoint_dir}")
    
    # 查找最新的checkpoint
    checkpoint_files = []
    for file in os.listdir(args.checkpoint_dir):
        if file.startswith('ckpt_epoch_') and file.endswith('.pth'):
            epoch_num = int(file.split('_')[2].split('.')[0])
            checkpoint_files.append((epoch_num, file))
    
    if not checkpoint_files:
        raise ValueError(f"No checkpoint files found in {args.checkpoint_dir}")
    
    # 选择最新的checkpoint
    latest_epoch, latest_checkpoint = max(checkpoint_files)
    checkpoint_path = os.path.join(args.checkpoint_dir, latest_checkpoint)
    
    print(f"Found latest checkpoint: {checkpoint_path} (epoch {latest_epoch})")
    
    # 读取原始配置 - 尝试JSON和YAML格式
    config_path_json = os.path.join(args.checkpoint_dir, 'config.json')
    config_path_yaml = os.path.join(args.checkpoint_dir, 'config.yaml')

    original_config = None
    if os.path.exists(config_path_json):
        try:
            with open(config_path_json, 'r') as f:
                original_config = json.load(f)
        except json.JSONDecodeError:
            # 如果JSON解析失败，尝试作为YAML读取
            with open(config_path_json, 'r') as f:
                original_config = yaml.safe_load(f)
    elif os.path.exists(config_path_yaml):
        with open(config_path_yaml, 'r') as f:
            original_config = yaml.safe_load(f)
    else:
        raise ValueError(f"Config file not found: {config_path_json} or {config_path_yaml}")

    if original_config is None:
        raise ValueError("Failed to load configuration file")
    
    # 检查数据集路径
    if not os.path.exists(args.data_path):
        raise ValueError(f"Dataset path does not exist: {args.data_path}")
    
    train_path = os.path.join(args.data_path, 'train')
    val_path = os.path.join(args.data_path, 'val')
    
    if not os.path.exists(train_path):
        raise ValueError(f"Train directory does not exist: {train_path}")
    if not os.path.exists(val_path):
        raise ValueError(f"Validation directory does not exist: {val_path}")
    
    # 检查类别数
    train_classes = len([d for d in os.listdir(train_path) 
                        if os.path.isdir(os.path.join(train_path, d))])
    
    print(f"Found {train_classes} classes in train set")
    
    # 确定模型配置文件
    model_name = original_config.get('MODEL', {}).get('NAME', '')
    if 'tiny' in model_name:
        config_file = 'classification/configs/vHeat/vHeat_tiny_imagenet100.yaml'
    elif 'small' in model_name:
        config_file = 'classification/configs/vHeat/vHeat_small_imagenet100.yaml'
    else:
        config_file = 'classification/configs/vHeat/vHeat_base_imagenet100.yaml'
    
    # 计算新的总epoch数
    new_total_epochs = latest_epoch + 1 + args.additional_epochs
    
    print(f"Continuing training from epoch {latest_epoch + 1} to epoch {new_total_epochs}")
    print(f"Early stopping patience: {args.patience} epochs")
    print(f"Minimum improvement threshold: {args.min_improvement}%")
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 构建训练命令
    if args.single_gpu:
        cmd = [
            'python', 'classification/main_early_stopping.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output,
            '--resume', checkpoint_path,
            '--local_rank', '0'
        ]
    else:
        cmd = [
            'torchrun',
            '--nnodes=1',
            '--nproc_per_node=1',
            '--master_addr=127.0.0.1',
            '--master_port=29502',
            'classification/main_early_stopping.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output,
            '--resume', checkpoint_path
        ]
    
    # 默认禁用EMA，除非明确启用
    if args.disable_ema or True:  # 强制禁用EMA
        cmd.extend(['--model_ema', 'False'])
    
    # 添加自定义配置选项
    cmd.extend([
        '--opts',
        'TRAIN.EPOCHS', str(new_total_epochs),
        'MODEL.NUM_CLASSES', str(train_classes),
        'EARLY_STOPPING.PATIENCE', str(args.patience),
        'EARLY_STOPPING.MIN_IMPROVEMENT', str(args.min_improvement)
    ])
    
    print("Running command:")
    print(' '.join(cmd))
    print()
    
    # 执行训练
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Training failed with error code {e.returncode}")
        return e.returncode
    
    print("Training completed successfully!")
    return 0

if __name__ == '__main__':
    exit(main())
